{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\data-news.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataNews = () => {\n  _s();\n  // Data News Component - Fixed Version\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const [news, setNews] = useState(null);\n  const [relatedNews, setRelatedNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: 'React News Portal'\n  });\n  const [bottomNav, setBottomNav] = useState(0);\n  useEffect(() => {\n    if (id) {\n      fetchNewsDetail(id);\n    }\n    // Fetch kostum data\n    fetchKostumData();\n  }, [id]);\n  const fetchKostumData = async () => {\n    try {\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings');\n      const data = await response.json();\n      if (data.success && data.data) {\n        const settings = data.data;\n        setKostum({\n          logo: settings.website_logo || '',\n          title: settings.website_name || 'React News Portal'\n        });\n      }\n    } catch (error) {\n      // Fallback to default values if API fails\n      setKostum({\n        logo: '',\n        title: 'React News Portal'\n      });\n    }\n  };\n  const fetchNewsDetail = async newsId => {\n    try {\n      setLoading(true);\n\n      // Fetch news detail (views already incremented from landing page)\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news_by_id&id=${newsId}`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && data.data) {\n        setNews(data.data);\n        // Fetch related news\n        if (data.data.category_id) {\n          fetchRelatedNews(data.data.category_id, newsId);\n        }\n      } else {\n        setError('Berita tidak ditemukan');\n      }\n    } catch (error) {\n      console.error('Error fetching news:', error);\n      setError('Gagal memuat berita. Silakan coba lagi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRelatedNews = async (categoryId, currentNewsId) => {\n    try {\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news&category=${categoryId}&limit=4`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      if (data.success && Array.isArray(data.data)) {\n        // Filter out current news and limit to 3\n        const filtered = data.data.filter(item => item.id !== parseInt(currentNewsId)).slice(0, 3);\n        setRelatedNews(filtered);\n      } else {\n        setRelatedNews([]);\n      }\n    } catch (error) {\n      console.error('Error fetching related news:', error);\n      setRelatedNews([]);\n    }\n  };\n  const getImageUrl = imagePath => {\n    if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n\n    // Jika sudah URL lengkap, gunakan langsung\n    if (imagePath.startsWith('http')) {\n      return imagePath;\n    }\n\n    // Extract filename from any path format\n    let filename = '';\n    if (imagePath.startsWith('/react-news/uploads/')) {\n      filename = imagePath.replace('/react-news/uploads/', '');\n    } else if (imagePath.startsWith('/uploads/')) {\n      filename = imagePath.replace('/uploads/', '');\n    } else if (imagePath.startsWith('assets/news/')) {\n      filename = imagePath.replace('assets/news/', '');\n    } else if (!imagePath.includes('/')) {\n      // Just filename\n      filename = imagePath;\n    } else {\n      // Extract filename from any other path\n      filename = imagePath.split('/').pop();\n    }\n\n    // Use root/uploads path for all images\n    const finalUrl = `http://localhost/react-news/uploads/${filename}`;\n    return finalUrl;\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleRelatedNewsClick = newsId => {\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleBackToHome = () => {\n    navigate('/');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat berita...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this);\n  }\n  if (!news) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Berita tidak ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBackToHome,\n          className: \"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-arrow-left mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 25\n          }, this), \"Kembali ke Beranda\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'gray.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: {\n            xs: 70,\n            md: 80\n          },\n          px: {\n            xs: 2,\n            md: 6\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png',\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: {\n                xs: 22,\n                md: 28\n              }\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/'),\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: 'white',\n        boxShadow: 1,\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        mt: {\n          xs: '70px',\n          md: '80px'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 2,\n          py: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1,\n            fontSize: '14px',\n            color: 'text.secondary'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"button\",\n            onClick: handleBackToHome,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main'\n              },\n              transition: 'color 0.2s',\n              border: 'none',\n              background: 'none',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-home\",\n              style: {\n                marginRight: '4px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 29\n            }, this), \"Beranda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-chevron-right\",\n            style: {\n              fontSize: '12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Detail Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        maxWidth: '1200px',\n        mx: 'auto',\n        px: 2,\n        py: 4,\n        pb: {\n          xs: 12,\n          md: 4\n        } // More padding on mobile for bottom nav\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            lg: '2fr 1fr'\n          },\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: getImageUrl(news.image),\n                alt: news.title,\n                className: \"w-full h-64 md:h-80 object-cover\",\n                onError: e => {\n                  e.target.src = 'https://picsum.photos/800/400?random=2';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                  children: news.category_name || 'Berita'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 md:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\",\n                children: news.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-calendar-alt mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 41\n                  }, this), formatDate(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-clock mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 41\n                  }, this), formatTime(news.created_at || news.date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-eye mr-2 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 41\n                  }, this), news.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"prose prose-lg max-w-none\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                  dangerouslySetInnerHTML: {\n                    __html: news.content.replace(/\\n/g, '<br>')\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-8 pt-6 border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"Bagikan Artikel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-facebook-f mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 45\n                    }, this), \"Facebook\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-twitter mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 45\n                    }, this), \"Twitter\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fab fa-whatsapp mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 45\n                    }, this), \"WhatsApp\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [relatedNews.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-newspaper mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 37\n              }, this), \"Berita Terkait\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: relatedNews.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleRelatedNewsClick(item.id),\n                className: \"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getImageUrl(item.image),\n                  alt: item.title,\n                  className: \"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\",\n                  onError: e => {\n                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-900 line-clamp-2 mb-1\",\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: formatDate(item.created_at || item.date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 45\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: 'white',\n              borderRadius: 2,\n              boxShadow: 2,\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"button\",\n              onClick: handleBackToHome,\n              sx: {\n                width: '100%',\n                bgcolor: 'primary.main',\n                color: 'white',\n                py: 1.5,\n                px: 2,\n                borderRadius: 2,\n                border: 'none',\n                cursor: 'pointer',\n                fontWeight: 500,\n                transition: 'background-color 0.2s',\n                '&:hover': {\n                  bgcolor: 'primary.dark'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-arrow-left\",\n                style: {\n                  marginRight: '8px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 33\n              }, this), \"Kembali ke Beranda\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: {\n          xs: 'block',\n          md: 'none'\n        },\n        // Hide on desktop\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item active\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'primary.main'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => navigate('/saved'),\n          className: \"bottom-nav-item\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 9\n  }, this);\n};\n_s(DataNews, \"b9Urwv16UwQ8sxy2aM1IgkkR+Js=\", false, function () {\n  return [useParams, useNavigate, useTheme, useMediaQuery];\n});\n_c = DataNews;\nexport default DataNews;\nvar _c;\n$RefreshReg$(_c, \"DataNews\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "AppBar", "<PERSON><PERSON><PERSON>", "Avatar", "IconButton", "MenuIcon", "useMediaQuery", "useTheme", "jsxDEV", "_jsxDEV", "DataNews", "_s", "id", "navigate", "theme", "isDesktop", "breakpoints", "up", "news", "setNews", "relatedNews", "setRelatedNews", "loading", "setLoading", "error", "setError", "kostum", "setKostum", "logo", "title", "bottomNav", "setBottomNav", "fetchNewsDetail", "fetchKostumData", "response", "fetch", "data", "json", "success", "settings", "website_logo", "website_name", "newsId", "ok", "Error", "status", "category_id", "fetchRelatedNews", "console", "categoryId", "currentNewsId", "Array", "isArray", "filtered", "filter", "item", "parseInt", "slice", "getImageUrl", "imagePath", "startsWith", "filename", "replace", "includes", "split", "pop", "finalUrl", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "handleRelatedNewsClick", "handleBackToHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "sx", "minHeight", "bgcolor", "width", "overflow", "position", "color", "elevation", "borderBottom", "borderColor", "zIndex", "xs", "md", "px", "display", "alignItems", "flexGrow", "src", "alt", "height", "mr", "onError", "e", "target", "variant", "fontWeight", "fontSize", "edge", "boxShadow", "mt", "max<PERSON><PERSON><PERSON>", "mx", "py", "gap", "component", "transition", "border", "background", "cursor", "style", "marginRight", "pb", "gridTemplateColumns", "lg", "image", "category_name", "created_at", "views", "dangerouslySetInnerHTML", "__html", "content", "length", "map", "borderRadius", "p", "left", "right", "bottom", "backgroundColor", "borderTop", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/data-news.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport Box from '@mui/material/Box';\nimport Typography from '@mui/material/Typography';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\n\nconst DataNews = () => {\n    // Data News Component - Fixed Version\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const theme = useTheme();\n    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n    const [news, setNews] = useState(null);\n    const [relatedNews, setRelatedNews] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [kostum, setKostum] = useState({ logo: '', title: 'React News Portal' });\n    const [bottomNav, setBottomNav] = useState(0);\n\n    useEffect(() => {\n        if (id) {\n            fetchNewsDetail(id);\n        }\n        // Fetch kostum data\n        fetchKostumData();\n    }, [id]);\n\n    const fetchKostumData = async () => {\n        try {\n            const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings');\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                const settings = data.data;\n                setKostum({\n                    logo: settings.website_logo || '',\n                    title: settings.website_name || 'React News Portal'\n                });\n            }\n        } catch (error) {\n            // Fallback to default values if API fails\n            setKostum({\n                logo: '',\n                title: 'React News Portal'\n            });\n        }\n    };\n\n    const fetchNewsDetail = async (newsId) => {\n        try {\n            setLoading(true);\n\n            // Fetch news detail (views already incremented from landing page)\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news_by_id&id=${newsId}`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && data.data) {\n                setNews(data.data);\n                // Fetch related news\n                if (data.data.category_id) {\n                    fetchRelatedNews(data.data.category_id, newsId);\n                }\n            } else {\n                setError('Berita tidak ditemukan');\n            }\n        } catch (error) {\n            console.error('Error fetching news:', error);\n            setError('Gagal memuat berita. Silakan coba lagi.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchRelatedNews = async (categoryId, currentNewsId) => {\n        try {\n            const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news&category=${categoryId}&limit=4`);\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const data = await response.json();\n\n            if (data.success && Array.isArray(data.data)) {\n                // Filter out current news and limit to 3\n                const filtered = data.data\n                    .filter(item => item.id !== parseInt(currentNewsId))\n                    .slice(0, 3);\n                setRelatedNews(filtered);\n            } else {\n                setRelatedNews([]);\n            }\n        } catch (error) {\n            console.error('Error fetching related news:', error);\n            setRelatedNews([]);\n        }\n    };\n\n    const getImageUrl = (imagePath) => {\n        if (!imagePath) return 'https://source.unsplash.com/800x400/?news';\n\n        // Jika sudah URL lengkap, gunakan langsung\n        if (imagePath.startsWith('http')) {\n            return imagePath;\n        }\n\n        // Extract filename from any path format\n        let filename = '';\n\n        if (imagePath.startsWith('/react-news/uploads/')) {\n            filename = imagePath.replace('/react-news/uploads/', '');\n        } else if (imagePath.startsWith('/uploads/')) {\n            filename = imagePath.replace('/uploads/', '');\n        } else if (imagePath.startsWith('assets/news/')) {\n            filename = imagePath.replace('assets/news/', '');\n        } else if (!imagePath.includes('/')) {\n            // Just filename\n            filename = imagePath;\n        } else {\n            // Extract filename from any other path\n            filename = imagePath.split('/').pop();\n        }\n\n        // Use root/uploads path for all images\n        const finalUrl = `http://localhost/react-news/uploads/${filename}`;\n        return finalUrl;\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleDateString('id-ID', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n\n    const formatTime = (dateString) => {\n        const date = new Date(dateString);\n        return date.toLocaleTimeString('id-ID', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleRelatedNewsClick = (newsId) => {\n        navigate(`/data-news/${newsId}`);\n    };\n\n    const handleBackToHome = () => {\n        navigate('/');\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Memuat berita...</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4\">\n                        <i className=\"fas fa-exclamation-triangle mr-2\"></i>\n                        {error}\n                    </div>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    if (!news) {\n        return (\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <p className=\"text-gray-600\">Berita tidak ditemukan</p>\n                    <button \n                        onClick={handleBackToHome}\n                        className=\"mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                        <i className=\"fas fa-arrow-left mr-2\"></i>\n                        Kembali ke Beranda\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <Box sx={{ minHeight: '100vh', bgcolor: 'gray.50', width: '100vw', overflow: 'hidden' }}>\n            {/* Responsive Navigation Bar */}\n            <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n                <Toolbar sx={{ minHeight: { xs: 70, md: 80 }, px: { xs: 2, md: 6 } }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n                        <Avatar\n                            src={kostum.logo ? getImageUrl(kostum.logo) : '/logo192.png'}\n                            alt=\"Logo\"\n                            sx={{ width: 48, height: 48, mr: 2 }}\n                            onError={(e) => { e.target.src = '/logo192.png'; }}\n                        />\n                        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: { xs: 22, md: 28 } }}>\n                            {kostum.title}\n                        </Typography>\n                    </Box>\n                    <IconButton\n                        edge=\"end\"\n                        color=\"primary\"\n                        onClick={() => navigate('/')}\n                        sx={{ mr: 1 }}\n                    >\n                        <MenuIcon fontSize=\"large\" />\n                    </IconButton>\n                </Toolbar>\n            </AppBar>\n\n            {/* Header/Breadcrumb */}\n            <Box sx={{ bgcolor: 'white', boxShadow: 1, borderBottom: 1, borderColor: 'grey.200', mt: { xs: '70px', md: '80px' } }}>\n                <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 2, py: 2 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '14px', color: 'text.secondary' }}>\n                        <Box\n                            component=\"button\"\n                            onClick={handleBackToHome}\n                            sx={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'text.secondary',\n                                '&:hover': { color: 'primary.main' },\n                                transition: 'color 0.2s',\n                                border: 'none',\n                                background: 'none',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            <i className=\"fas fa-home\" style={{ marginRight: '4px' }}></i>\n                            Beranda\n                        </Box>\n                        <i className=\"fas fa-chevron-right\" style={{ fontSize: '12px' }}></i>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n                            Detail Berita\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box sx={{\n                maxWidth: '1200px',\n                mx: 'auto',\n                px: 2,\n                py: 4,\n                pb: { xs: 12, md: 4 } // More padding on mobile for bottom nav\n            }}>\n                <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' }, gap: 4 }}>\n                    {/* Main Article */}\n                    <Box>\n                        <article className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n                            {/* Article Header */}\n                            <div className=\"relative\">\n                                <img \n                                    src={getImageUrl(news.image)} \n                                    alt={news.title}\n                                    className=\"w-full h-64 md:h-80 object-cover\"\n                                    onError={(e) => {\n                                        e.target.src = 'https://picsum.photos/800/400?random=2';\n                                    }}\n                                />\n                                <div className=\"absolute top-4 left-4\">\n                                    <span className=\"bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                                        {news.category_name || 'Berita'}\n                                    </span>\n                                </div>\n                            </div>\n\n                            {/* Article Content */}\n                            <div className=\"p-6 md:p-8\">\n                                <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4 leading-tight\">\n                                    {news.title}\n                                </h1>\n\n                                {/* Meta Information */}\n                                <div className=\"flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6 pb-6 border-b\">\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-calendar-alt mr-2 text-blue-600\"></i>\n                                        {formatDate(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-clock mr-2 text-blue-600\"></i>\n                                        {formatTime(news.created_at || news.date)}\n                                    </div>\n                                    <div className=\"flex items-center\">\n                                        <i className=\"fas fa-eye mr-2 text-blue-600\"></i>\n                                        {news.views || 0} views\n                                    </div>\n                                </div>\n\n                                {/* Article Body */}\n                                <div className=\"prose prose-lg max-w-none\">\n                                    <div \n                                        className=\"text-gray-700 leading-relaxed whitespace-pre-line\"\n                                        dangerouslySetInnerHTML={{ \n                                            __html: news.content.replace(/\\n/g, '<br>') \n                                        }}\n                                    />\n                                </div>\n\n                                {/* Share Buttons */}\n                                <div className=\"mt-8 pt-6 border-t\">\n                                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Bagikan Artikel</h3>\n                                    <div className=\"flex space-x-3\">\n                                        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                                            <i className=\"fab fa-facebook-f mr-2\"></i>\n                                            Facebook\n                                        </button>\n                                        <button className=\"bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors\">\n                                            <i className=\"fab fa-twitter mr-2\"></i>\n                                            Twitter\n                                        </button>\n                                        <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                                            <i className=\"fab fa-whatsapp mr-2\"></i>\n                                            WhatsApp\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </article>\n                    </Box>\n\n                    {/* Sidebar */}\n                    <Box>\n                        {/* Related News */}\n                        {relatedNews.length > 0 && (\n                            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\n                                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                                    <i className=\"fas fa-newspaper mr-2 text-blue-600\"></i>\n                                    Berita Terkait\n                                </h3>\n                                <div className=\"space-y-4\">\n                                    {relatedNews.map((item) => (\n                                        <div \n                                            key={item.id}\n                                            onClick={() => handleRelatedNewsClick(item.id)}\n                                            className=\"flex cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors\"\n                                        >\n                                            <img \n                                                src={getImageUrl(item.image)} \n                                                alt={item.title}\n                                                className=\"w-16 h-16 object-cover rounded-lg mr-3 flex-shrink-0\"\n                                                onError={(e) => {\n                                                    e.target.src = 'https://picsum.photos/150/150?random=3';\n                                                }}\n                                            />\n                                            <div className=\"flex-1 min-w-0\">\n                                                <h4 className=\"text-sm font-medium text-gray-900 line-clamp-2 mb-1\">\n                                                    {item.title}\n                                                </h4>\n                                                <p className=\"text-xs text-gray-600\">\n                                                    {formatDate(item.created_at || item.date)}\n                                                </p>\n                                            </div>\n                                        </div>\n                                    ))}\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Back to Home Button */}\n                        <Box sx={{ bgcolor: 'white', borderRadius: 2, boxShadow: 2, p: 3 }}>\n                            <Box\n                                component=\"button\"\n                                onClick={handleBackToHome}\n                                sx={{\n                                    width: '100%',\n                                    bgcolor: 'primary.main',\n                                    color: 'white',\n                                    py: 1.5,\n                                    px: 2,\n                                    borderRadius: 2,\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontWeight: 500,\n                                    transition: 'background-color 0.2s',\n                                    '&:hover': {\n                                        bgcolor: 'primary.dark'\n                                    }\n                                }}\n                            >\n                                <i className=\"fas fa-arrow-left\" style={{ marginRight: '8px' }}></i>\n                                Kembali ke Beranda\n                            </Box>\n                        </Box>\n                    </Box>\n                </Box>\n            </Box>\n\n            {/* Custom Bottom Navigation - Mobile Only */}\n            <Box sx={{\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 1300,\n                display: { xs: 'block', md: 'none' }, // Hide on desktop\n                backgroundColor: 'white',\n                borderTop: '1px solid #e0e0e0',\n                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n            }}>\n                <Box sx={{\n                    display: 'flex',\n                    justifyContent: 'space-around',\n                    alignItems: 'center',\n                    height: 64,\n                    px: 1\n                }}>\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item active\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-home bottom-nav-icon text-blue-600\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'primary.main' }}>\n                            Home\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-search bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Cari\n                        </Typography>\n                    </Box>\n\n                    <Box\n                        onClick={() => navigate('/saved')}\n                        className=\"bottom-nav-item\"\n                        style={{ cursor: 'pointer' }}\n                    >\n                        <i className=\"fas fa-bookmark bottom-nav-icon text-gray-500\"></i>\n                        <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: 'text.secondary' }}>\n                            Simpan\n                        </Typography>\n                    </Box>\n                </Box>\n            </Box>\n        </Box>\n    );\n};\n\nexport default DataNews;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB;EACA,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,KAAK,GAAGP,QAAQ,CAAC,CAAC;EACxB,MAAMQ,SAAS,GAAGT,aAAa,CAACQ,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAoB,CAAC,CAAC;EAC9E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACZ,IAAIgB,EAAE,EAAE;MACJoB,eAAe,CAACpB,EAAE,CAAC;IACvB;IACA;IACAqB,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrB,EAAE,CAAC,CAAC;EAER,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kFAAkF,CAAC;MAChH,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC3B,MAAMG,QAAQ,GAAGH,IAAI,CAACA,IAAI;QAC1BT,SAAS,CAAC;UACNC,IAAI,EAAEW,QAAQ,CAACC,YAAY,IAAI,EAAE;UACjCX,KAAK,EAAEU,QAAQ,CAACE,YAAY,IAAI;QACpC,CAAC,CAAC;MACN;IACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZ;MACAG,SAAS,CAAC;QACNC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACX,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMG,eAAe,GAAG,MAAOU,MAAM,IAAK;IACtC,IAAI;MACAnB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,yFAAyFO,MAAM,EAAE,CAAC;MAE/H,IAAI,CAACR,QAAQ,CAACS,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBV,QAAQ,CAACW,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC3BjB,OAAO,CAACiB,IAAI,CAACA,IAAI,CAAC;QAClB;QACA,IAAIA,IAAI,CAACA,IAAI,CAACU,WAAW,EAAE;UACvBC,gBAAgB,CAACX,IAAI,CAACA,IAAI,CAACU,WAAW,EAAEJ,MAAM,CAAC;QACnD;MACJ,CAAC,MAAM;QACHjB,QAAQ,CAAC,wBAAwB,CAAC;MACtC;IACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZwB,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,yCAAyC,CAAC;IACvD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAAA,CAAOE,UAAU,EAAEC,aAAa,KAAK;IAC1D,IAAI;MACA,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAAC,yFAAyFc,UAAU,UAAU,CAAC;MAE3I,IAAI,CAACf,QAAQ,CAACS,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBV,QAAQ,CAACW,MAAM,EAAE,CAAC;MAC7D;MAEA,MAAMT,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIa,KAAK,CAACC,OAAO,CAAChB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC1C;QACA,MAAMiB,QAAQ,GAAGjB,IAAI,CAACA,IAAI,CACrBkB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC3C,EAAE,KAAK4C,QAAQ,CAACN,aAAa,CAAC,CAAC,CACnDO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAChBpC,cAAc,CAACgC,QAAQ,CAAC;MAC5B,CAAC,MAAM;QACHhC,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACZwB,OAAO,CAACxB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDH,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMqC,WAAW,GAAIC,SAAS,IAAK;IAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,2CAA2C;;IAElE;IACA,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC9B,OAAOD,SAAS;IACpB;;IAEA;IACA,IAAIE,QAAQ,GAAG,EAAE;IAEjB,IAAIF,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;MAC9CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;IAC5D,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;MAC1CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACjD,CAAC,MAAM,IAAIH,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;MAC7CC,QAAQ,GAAGF,SAAS,CAACG,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;IACpD,CAAC,MAAM,IAAI,CAACH,SAAS,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACAF,QAAQ,GAAGF,SAAS;IACxB,CAAC,MAAM;MACH;MACAE,QAAQ,GAAGF,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;IACzC;;IAEA;IACA,MAAMC,QAAQ,GAAG,uCAAuCL,QAAQ,EAAE;IAClE,OAAOK,QAAQ;EACnB,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACT,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAIR,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,sBAAsB,GAAItC,MAAM,IAAK;IACvC7B,QAAQ,CAAC,cAAc6B,MAAM,EAAE,CAAC;EACpC,CAAC;EAED,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BpE,QAAQ,CAAC,GAAG,CAAC;EACjB,CAAC;EAED,IAAIS,OAAO,EAAE;IACT,oBACIb,OAAA;MAAKyE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE1E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB1E,OAAA;UAAKyE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG9E,OAAA;UAAGyE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI/D,KAAK,EAAE;IACP,oBACIf,OAAA;MAAKyE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE1E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB1E,OAAA;UAAKyE,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACpF1E,OAAA;YAAGyE,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACnD/D,KAAK;QAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9E,OAAA;UACI+E,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,gBAE3F1E,OAAA;YAAGyE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI,CAACrE,IAAI,EAAE;IACP,oBACIT,OAAA;MAAKyE,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACrE1E,OAAA;QAAKyE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB1E,OAAA;UAAGyE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvD9E,OAAA;UACI+E,OAAO,EAAEP,gBAAiB;UAC1BC,SAAS,EAAC,sFAAsF;UAAAC,QAAA,gBAEhG1E,OAAA;YAAGyE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,sBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI9E,OAAA,CAACV,GAAG;IAAC0F,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAV,QAAA,gBAEpF1E,OAAA,CAACR,MAAM;MAAC6F,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACP,EAAE,EAAE;QAAEE,OAAO,EAAE,MAAM;QAAEM,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAhB,QAAA,eACnI1E,OAAA,CAACP,OAAO;QAACuF,EAAE,EAAE;UAAEC,SAAS,EAAE;YAAEU,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAAEC,EAAE,EAAE;YAAEF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE;QAAE,CAAE;QAAAlB,QAAA,gBACjE1E,OAAA,CAACV,GAAG;UAAC0F,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBAC5D1E,OAAA,CAACN,MAAM;YACHuG,GAAG,EAAEhF,MAAM,CAACE,IAAI,GAAG8B,WAAW,CAAChC,MAAM,CAACE,IAAI,CAAC,GAAG,cAAe;YAC7D+E,GAAG,EAAC,MAAM;YACVlB,EAAE,EAAE;cAAEG,KAAK,EAAE,EAAE;cAAEgB,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACF9E,OAAA,CAACT,UAAU;YAACiH,OAAO,EAAC,IAAI;YAACxB,EAAE,EAAE;cAAEyB,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEoB,QAAQ,EAAE;gBAAEf,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAAE,CAAE;YAAAlB,QAAA,EACjGzD,MAAM,CAACG;UAAK;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACN9E,OAAA,CAACL,UAAU;UACPgH,IAAI,EAAC,KAAK;UACVrB,KAAK,EAAC,SAAS;UACfP,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,GAAG,CAAE;UAC7B4E,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAEd1E,OAAA,CAACJ,QAAQ;YAAC8G,QAAQ,EAAC;UAAO;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGT9E,OAAA,CAACV,GAAG;MAAC0F,EAAE,EAAE;QAAEE,OAAO,EAAE,OAAO;QAAE0B,SAAS,EAAE,CAAC;QAAEpB,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QAAEoB,EAAE,EAAE;UAAElB,EAAE,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAO;MAAE,CAAE;MAAAlB,QAAA,eAClH1E,OAAA,CAACV,GAAG;QAAC0F,EAAE,EAAE;UAAE8B,QAAQ,EAAE,QAAQ;UAAEC,EAAE,EAAE,MAAM;UAAElB,EAAE,EAAE,CAAC;UAAEmB,EAAE,EAAE;QAAE,CAAE;QAAAtC,QAAA,eACtD1E,OAAA,CAACV,GAAG;UAAC0F,EAAE,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEkB,GAAG,EAAE,CAAC;YAAEP,QAAQ,EAAE,MAAM;YAAEpB,KAAK,EAAE;UAAiB,CAAE;UAAAZ,QAAA,gBAClG1E,OAAA,CAACV,GAAG;YACA4H,SAAS,EAAC,QAAQ;YAClBnC,OAAO,EAAEP,gBAAiB;YAC1BQ,EAAE,EAAE;cACAc,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBT,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBAAEA,KAAK,EAAE;cAAe,CAAC;cACpC6B,UAAU,EAAE,YAAY;cACxBC,MAAM,EAAE,MAAM;cACdC,UAAU,EAAE,MAAM;cAClBC,MAAM,EAAE;YACZ,CAAE;YAAA5C,QAAA,gBAEF1E,OAAA;cAAGyE,SAAS,EAAC,aAAa;cAAC8C,KAAK,EAAE;gBAAEC,WAAW,EAAE;cAAM;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,WAElE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN9E,OAAA;YAAGyE,SAAS,EAAC,sBAAsB;YAAC8C,KAAK,EAAE;cAAEb,QAAQ,EAAE;YAAO;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE9E,OAAA,CAACT,UAAU;YAACiH,OAAO,EAAC,OAAO;YAACxB,EAAE,EAAE;cAAEyB,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE;YAAe,CAAE;YAAAZ,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN9E,OAAA,CAACV,GAAG;MAAC0F,EAAE,EAAE;QACL8B,QAAQ,EAAE,QAAQ;QAClBC,EAAE,EAAE,MAAM;QACVlB,EAAE,EAAE,CAAC;QACLmB,EAAE,EAAE,CAAC;QACLS,EAAE,EAAE;UAAE9B,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC,CAAC;MAC1B,CAAE;MAAAlB,QAAA,eACE1E,OAAA,CAACV,GAAG;QAAC0F,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAE4B,mBAAmB,EAAE;YAAE/B,EAAE,EAAE,KAAK;YAAEgC,EAAE,EAAE;UAAU,CAAC;UAAEV,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBAEpF1E,OAAA,CAACV,GAAG;UAAAoF,QAAA,eACA1E,OAAA;YAASyE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAE9D1E,OAAA;cAAKyE,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB1E,OAAA;gBACIiG,GAAG,EAAEhD,WAAW,CAACxC,IAAI,CAACmH,KAAK,CAAE;gBAC7B1B,GAAG,EAAEzF,IAAI,CAACW,KAAM;gBAChBqD,SAAS,EAAC,kCAAkC;gBAC5C4B,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wCAAwC;gBAC3D;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACF9E,OAAA;gBAAKyE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAClC1E,OAAA;kBAAMyE,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAC9EjE,IAAI,CAACoH,aAAa,IAAI;gBAAQ;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGN9E,OAAA;cAAKyE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB1E,OAAA;gBAAIyE,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAC1EjE,IAAI,CAACW;cAAK;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eAGL9E,OAAA;gBAAKyE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,gBACvF1E,OAAA;kBAAKyE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B1E,OAAA;oBAAGyE,SAAS,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzDpB,UAAU,CAACjD,IAAI,CAACqH,UAAU,IAAIrH,IAAI,CAACmD,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN9E,OAAA;kBAAKyE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B1E,OAAA;oBAAGyE,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClDX,UAAU,CAAC1D,IAAI,CAACqH,UAAU,IAAIrH,IAAI,CAACmD,IAAI,CAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN9E,OAAA;kBAAKyE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC9B1E,OAAA;oBAAGyE,SAAS,EAAC;kBAA+B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAChDrE,IAAI,CAACsH,KAAK,IAAI,CAAC,EAAC,QACrB;gBAAA;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN9E,OAAA;gBAAKyE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtC1E,OAAA;kBACIyE,SAAS,EAAC,mDAAmD;kBAC7DuD,uBAAuB,EAAE;oBACrBC,MAAM,EAAExH,IAAI,CAACyH,OAAO,CAAC7E,OAAO,CAAC,KAAK,EAAE,MAAM;kBAC9C;gBAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN9E,OAAA;gBAAKyE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAC/B1E,OAAA;kBAAIyE,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7E9E,OAAA;kBAAKyE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B1E,OAAA;oBAAQyE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/F1E,OAAA;sBAAGyE,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBAAQyE,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,gBAC/F1E,OAAA;sBAAGyE,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9E,OAAA;oBAAQyE,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBACjG1E,OAAA;sBAAGyE,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGN9E,OAAA,CAACV,GAAG;UAAAoF,QAAA,GAEC/D,WAAW,CAACwH,MAAM,GAAG,CAAC,iBACnBnI,OAAA;YAAKyE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACnD1E,OAAA;cAAIyE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACpD1E,OAAA;gBAAGyE,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cAAKyE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACrB/D,WAAW,CAACyH,GAAG,CAAEtF,IAAI,iBAClB9C,OAAA;gBAEI+E,OAAO,EAAEA,CAAA,KAAMR,sBAAsB,CAACzB,IAAI,CAAC3C,EAAE,CAAE;gBAC/CsE,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBAEjF1E,OAAA;kBACIiG,GAAG,EAAEhD,WAAW,CAACH,IAAI,CAAC8E,KAAK,CAAE;kBAC7B1B,GAAG,EAAEpD,IAAI,CAAC1B,KAAM;kBAChBqD,SAAS,EAAC,sDAAsD;kBAChE4B,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,wCAAwC;kBAC3D;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACF9E,OAAA;kBAAKyE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC3B1E,OAAA;oBAAIyE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAC9D5B,IAAI,CAAC1B;kBAAK;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL9E,OAAA;oBAAGyE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAC/BhB,UAAU,CAACZ,IAAI,CAACgF,UAAU,IAAIhF,IAAI,CAACc,IAAI;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAnBDhC,IAAI,CAAC3C,EAAE;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,eAGD9E,OAAA,CAACV,GAAG;YAAC0F,EAAE,EAAE;cAAEE,OAAO,EAAE,OAAO;cAAEmD,YAAY,EAAE,CAAC;cAAEzB,SAAS,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAAA5D,QAAA,eAC/D1E,OAAA,CAACV,GAAG;cACA4H,SAAS,EAAC,QAAQ;cAClBnC,OAAO,EAAEP,gBAAiB;cAC1BQ,EAAE,EAAE;gBACAG,KAAK,EAAE,MAAM;gBACbD,OAAO,EAAE,cAAc;gBACvBI,KAAK,EAAE,OAAO;gBACd0B,EAAE,EAAE,GAAG;gBACPnB,EAAE,EAAE,CAAC;gBACLwC,YAAY,EAAE,CAAC;gBACfjB,MAAM,EAAE,MAAM;gBACdE,MAAM,EAAE,SAAS;gBACjBb,UAAU,EAAE,GAAG;gBACfU,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACPjC,OAAO,EAAE;gBACb;cACJ,CAAE;cAAAR,QAAA,gBAEF1E,OAAA;gBAAGyE,SAAS,EAAC,mBAAmB;gBAAC8C,KAAK,EAAE;kBAAEC,WAAW,EAAE;gBAAM;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sBAExE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN9E,OAAA,CAACV,GAAG;MAAC0F,EAAE,EAAE;QACLK,QAAQ,EAAE,OAAO;QACjBkD,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT/C,MAAM,EAAE,IAAI;QACZI,OAAO,EAAE;UAAEH,EAAE,EAAE,OAAO;UAAEC,EAAE,EAAE;QAAO,CAAC;QAAE;QACtC8C,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,mBAAmB;QAC9B/B,SAAS,EAAE;MACf,CAAE;MAAAlC,QAAA,eACE1E,OAAA,CAACV,GAAG;QAAC0F,EAAE,EAAE;UACLc,OAAO,EAAE,MAAM;UACf8C,cAAc,EAAE,cAAc;UAC9B7C,UAAU,EAAE,QAAQ;UACpBI,MAAM,EAAE,EAAE;UACVN,EAAE,EAAE;QACR,CAAE;QAAAnB,QAAA,gBACE1E,OAAA,CAACV,GAAG;UACAyF,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,GAAG,CAAE;UAC7BqE,SAAS,EAAC,wBAAwB;UAClC8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7B1E,OAAA;YAAGyE,SAAS,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D9E,OAAA,CAACT,UAAU;YAACiH,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAe,CAAE;YAAAZ,QAAA,EAAC;UAE1F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN9E,OAAA,CAACV,GAAG;UACAyF,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,GAAG,CAAE;UAC7BqE,SAAS,EAAC,iBAAiB;UAC3B8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7B1E,OAAA;YAAGyE,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/D9E,OAAA,CAACT,UAAU;YAACiH,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN9E,OAAA,CAACV,GAAG;UACAyF,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,QAAQ,CAAE;UAClCqE,SAAS,EAAC,iBAAiB;UAC3B8C,KAAK,EAAE;YAAED,MAAM,EAAE;UAAU,CAAE;UAAA5C,QAAA,gBAE7B1E,OAAA;YAAGyE,SAAS,EAAC;UAA+C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE9E,OAAA,CAACT,UAAU;YAACiH,OAAO,EAAC,SAAS;YAAC/B,SAAS,EAAC,kBAAkB;YAACO,EAAE,EAAE;cAAEM,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC5E,EAAA,CA9cID,QAAQ;EAAA,QAEKb,SAAS,EACPC,WAAW,EACdS,QAAQ,EACJD,aAAa;AAAA;AAAAgJ,EAAA,GAL7B5I,QAAQ;AAgdd,eAAeA,QAAQ;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}