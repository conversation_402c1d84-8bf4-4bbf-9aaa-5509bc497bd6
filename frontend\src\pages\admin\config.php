<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'react_news');

// Website settings
$website_settings = [
    'name' => 'React News Portal',
    'logo' => 'assets/logo.png',
    'primary_color' => '#3B82F6',
    'secondary_color' => '#1E40AF',
    'accent_color' => '#F59E0B'
];

// Database connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Create tables if they don't exist
function createTables() {
    $pdo = getConnection();

    // Categories table first (referenced by posts)
    $sql = "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        color VARCHAR(7) DEFAULT '#3B82F6',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Insert default categories if not exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    $categoryCount = $stmt->fetchColumn();

    if ($categoryCount == 0) {
        $defaultCategories = [
            ['Umum', '#3B82F6'],
            ['Politik', '#EF4444'],
            ['Ekonomi', '#10B981'],
            ['Olahraga', '#F59E0B'],
            ['Teknologi', '#8B5CF6'],
            ['Hiburan', '#EC4899'],
            ['Kesehatan', '#06B6D4']
        ];

        $stmt = $pdo->prepare("INSERT INTO categories (name, color) VALUES (?, ?)");
        foreach ($defaultCategories as $category) {
            $stmt->execute($category);
        }
    }

    // Posts table (matching the actual database structure)
    $sql = "CREATE TABLE IF NOT EXISTS posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NULL,
        description TEXT NULL,
        content LONGTEXT NOT NULL,
        excerpt TEXT NULL,
        image VARCHAR(255) NULL,
        image_alt VARCHAR(255) NULL,
        category_id INT DEFAULT 1,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        featured TINYINT(1) DEFAULT 0,
        meta_title VARCHAR(255) NULL,
        meta_description TEXT NULL,
        tags TEXT NULL,
        share INT DEFAULT 0,
        views INT DEFAULT 0,
        likes INT DEFAULT 0,
        comments_count INT DEFAULT 0,
        reading_time INT DEFAULT 0,
        published_at DATETIME NULL,
        date DATETIME DEFAULT CURRENT_TIMESTAMP,
        user_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id)
    )";
    $pdo->exec($sql);

    // Admin table
    $sql = "CREATE TABLE IF NOT EXISTS admin (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Settings table
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Saved posts table
    $sql = "CREATE TABLE IF NOT EXISTS saved (
        id INT AUTO_INCREMENT PRIMARY KEY,
        post_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);

    // Notifications table
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('success', 'info', 'warning', 'error') DEFAULT 'info',
        action_type ENUM('create', 'update', 'delete', 'upload') NOT NULL,
        entity_type ENUM('news', 'video', 'user', 'category', 'settings') NOT NULL,
        entity_id INT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_created_at (created_at),
        INDEX idx_is_read (is_read)
    )";
    $pdo->exec($sql);

    // Insert default settings if not exist
    $settings = [
        ['website_name', 'React News Portal'],
        ['website_logo', 'assets/logo.png'],
        ['primary_color', '#3B82F6'],
        ['secondary_color', '#1E40AF'],
        ['accent_color', '#F59E0B']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }

    // Add sample notifications if notifications table is empty
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications");
    $stmt->execute();
    $notificationCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    if ($notificationCount == 0) {
        $sampleNotifications = [
            [
                'Selamat Datang di Admin Dashboard',
                'Sistem notifikasi telah berhasil diaktifkan. Anda akan menerima notifikasi untuk semua aktivitas CRUD.',
                'create',
                'settings',
                'info'
            ],
            [
                'Sistem Siap Digunakan',
                'Dashboard admin telah berhasil dikonfigurasi dan siap untuk mengelola konten website.',
                'create',
                'settings',
                'success'
            ]
        ];

        $stmt = $pdo->prepare("INSERT INTO notifications (title, message, action_type, entity_type, type) VALUES (?, ?, ?, ?, ?)");
        foreach ($sampleNotifications as $notification) {
            $stmt->execute($notification);
        }
    }

    // Insert default admin if not exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();

    if ($adminCount == 0) {
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin (username, password) VALUES (?, ?)");
        $stmt->execute(['admin', $defaultPassword]);
    }

    // Sample posts creation disabled - only create when explicitly needed
    // Data will be created manually through admin interface
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts");
    $stmt->execute();
    $postCount = $stmt->fetchColumn();

    if (false && $postCount == 0) { // Disabled automatic sample data creation
        $samplePosts = [
            [
                'title' => 'Berita Teknologi Terbaru 2024',
                'slug' => 'berita-teknologi-terbaru-2024',
                'description' => 'Perkembangan teknologi terbaru yang mengubah dunia di tahun 2024',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
                'excerpt' => 'Perkembangan teknologi terbaru yang mengubah dunia di tahun 2024',
                'category_id' => 5, // Teknologi
                'status' => 'published',
                'views' => 150,
                'likes' => 25,
                'share' => 10
            ],
            [
                'title' => 'Update Ekonomi Indonesia Hari Ini',
                'slug' => 'update-ekonomi-indonesia-hari-ini',
                'description' => 'Kondisi perekonomian Indonesia terkini dan proyeksi ke depan',
                'content' => 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.',
                'excerpt' => 'Kondisi perekonomian Indonesia terkini dan proyeksi ke depan',
                'category_id' => 3, // Ekonomi
                'status' => 'published',
                'views' => 89,
                'likes' => 12,
                'share' => 5
            ],
            [
                'title' => 'Hasil Pertandingan Liga Indonesia',
                'slug' => 'hasil-pertandingan-liga-indonesia',
                'description' => 'Rangkuman hasil pertandingan liga Indonesia pekan ini',
                'content' => 'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi.',
                'excerpt' => 'Rangkuman hasil pertandingan liga Indonesia pekan ini',
                'category_id' => 4, // Olahraga
                'status' => 'published',
                'views' => 234,
                'likes' => 45,
                'share' => 18
            ]
        ];

        $stmt = $pdo->prepare("
            INSERT INTO posts (
                title, slug, description, content, excerpt, category_id,
                status, views, likes, share, reading_time, published_at,
                date, user_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 1, NOW(), NOW())
        ");

        foreach ($samplePosts as $post) {
            $readingTime = max(1, ceil(str_word_count($post['content']) / 200));
            $stmt->execute([
                $post['title'], $post['slug'], $post['description'],
                $post['content'], $post['excerpt'], $post['category_id'],
                $post['status'], $post['views'], $post['likes'],
                $post['share'], $readingTime
            ]);
        }
    }
}

// Initialize database
createTables();

// Helper functions for settings
function getSetting($key) {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        if ($result) {
            return $result['setting_value'];
        }

        // Return defaults for common settings if not found
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    } catch (PDOException $e) {
        error_log("Error getting setting $key: " . $e->getMessage());

        // Return defaults if database error
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    }
}

function updateSetting($key, $value) {
    try {
        $pdo = getConnection();

        // Use simple key-value structure
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value)
                              VALUES (?, ?)
                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");

        $result = $stmt->execute([$key, $value, $value]);

        if (!$result) {
            error_log("Failed to update setting $key: " . implode(', ', $stmt->errorInfo()));
            return false;
        }

        return true;
    } catch (PDOException $e) {
        error_log("Error updating setting $key: " . $e->getMessage());
        return false;
    }
}

// generateSlug function is defined in api.php to avoid redeclaration

// Function to get dashboard statistics
function getStats() {
    try {
        $pdo = getConnection();

        // Get total admin users
        $totalUsers = $pdo->query("SELECT COUNT(*) FROM admin")->fetchColumn();

        // Get total posts/news
        $totalNews = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();

        // Get published news
        $publishedNews = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'published'")->fetchColumn();

        // Get draft news
        $draftNews = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'draft'")->fetchColumn();

        // Get total categories
        $totalCategories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();

        // Get total views
        $pageViews = $pdo->query("SELECT SUM(views) FROM posts")->fetchColumn() ?: 0;

        // Get total shares
        $totalShares = $pdo->query("SELECT SUM(share) FROM posts")->fetchColumn() ?: 0;

        // Get total likes
        $totalLikes = $pdo->query("SELECT SUM(likes) FROM posts")->fetchColumn() ?: 0;

        // Get total comments
        $totalComments = $pdo->query("SELECT SUM(comments_count) FROM posts")->fetchColumn() ?: 0;

        // Get total saved posts
        $savedPosts = $pdo->query("SELECT COUNT(*) FROM saved")->fetchColumn();

        return [
            'total_users' => (int)$totalUsers,
            'total_news' => (int)$totalNews,
            'published_news' => (int)$publishedNews,
            'draft_news' => (int)$draftNews,
            'total_categories' => (int)$totalCategories,
            'page_views' => (int)$pageViews,
            'total_shares' => (int)$totalShares,
            'total_likes' => (int)$totalLikes,
            'total_comments' => (int)$totalComments,
            'saved_posts' => (int)$savedPosts,
            'revenue' => (int)($totalShares * 100) // Placeholder calculation
        ];
    } catch (Exception $e) {
        error_log("Error in getStats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'total_news' => 0,
            'published_news' => 0,
            'draft_news' => 0,
            'total_categories' => 0,
            'page_views' => 0,
            'total_shares' => 0,
            'total_likes' => 0,
            'total_comments' => 0,
            'saved_posts' => 0,
            'revenue' => 0
        ];
    }
}

// Helper functions for categories
function getCategories() {
    try {
        $pdo = getConnection();

        // Check if categories table exists, if not create it with proper structure
        $stmt = $pdo->query("SHOW TABLES LIKE 'categories'");
        if ($stmt->rowCount() == 0) {
            // Create categories table with full structure matching create_missing_tables.sql
            $createTable = "CREATE TABLE IF NOT EXISTS `categories` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `slug` varchar(100) NOT NULL,
                `description` text,
                `color` varchar(7) DEFAULT '#6B7280',
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `slug` (`slug`),
                KEY `is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
            $pdo->exec($createTable);

            // Insert default categories with proper structure
            $defaultCategories = [
                [1, 'Umum', 'umum', 'Berita umum dan informasi', '#6B7280', 1],
                [2, 'Teknologi', 'teknologi', 'Berita teknologi dan inovasi', '#3B82F6', 1],
                [3, 'Bisnis', 'bisnis', 'Berita bisnis dan ekonomi', '#10B981', 1],
                [4, 'Olahraga', 'olahraga', 'Berita olahraga dan kompetisi', '#F59E0B', 1],
                [5, 'Hiburan', 'hiburan', 'Berita hiburan dan selebriti', '#EF4444', 1],
                [6, 'Politik', 'politik', 'Berita politik dan pemerintahan', '#8B5CF6', 1],
                [7, 'Kesehatan', 'kesehatan', 'Berita kesehatan dan medis', '#06B6D4', 1]
            ];

            $insertStmt = $pdo->prepare("INSERT IGNORE INTO categories (id, name, slug, description, color, is_active) VALUES (?, ?, ?, ?, ?, ?)");
            foreach ($defaultCategories as $category) {
                $insertStmt->execute($category);
            }
        }

        // Select only active categories with post count
        $stmt = $pdo->query("
            SELECT
                c.*,
                COUNT(p.id) as post_count
            FROM categories c
            LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.name ASC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getCategories: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());

        // Return default categories if database error
        return [
            ['id' => 1, 'name' => 'Umum', 'slug' => 'umum', 'color' => '#6B7280', 'is_active' => 1],
            ['id' => 2, 'name' => 'Teknologi', 'slug' => 'teknologi', 'color' => '#3B82F6', 'is_active' => 1],
            ['id' => 3, 'name' => 'Bisnis', 'slug' => 'bisnis', 'color' => '#10B981', 'is_active' => 1],
            ['id' => 4, 'name' => 'Olahraga', 'slug' => 'olahraga', 'color' => '#F59E0B', 'is_active' => 1],
            ['id' => 5, 'name' => 'Hiburan', 'slug' => 'hiburan', 'color' => '#EF4444', 'is_active' => 1],
            ['id' => 6, 'name' => 'Politik', 'slug' => 'politik', 'color' => '#8B5CF6', 'is_active' => 1],
            ['id' => 7, 'name' => 'Kesehatan', 'slug' => 'kesehatan', 'color' => '#06B6D4', 'is_active' => 1]
        ];
    }
}

// Function to get recent activities
function getRecentActivities($limit = 5) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.id, p.title, p.created_at, 'post' as type
            FROM posts p
            ORDER BY p.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([(int)$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getRecentActivities: " . $e->getMessage());
        return [];
    }
}

// Function to get popular posts
function getPopularPosts($limit = 5) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'published'
            ORDER BY p.views DESC
            LIMIT ?
        ");
        $stmt->execute([(int)$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getPopularPosts: " . $e->getMessage());
        return [];
    }
}

// Saved news functions are defined in api.php to avoid redeclaration

function isSavedNews($postId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM saved WHERE post_id = ?");
        $stmt->execute([$postId]);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Error in isSavedNews: " . $e->getMessage());
        return false;
    }
}

// Notification functions
function addNotification($title, $message, $action_type, $entity_type, $type = 'info', $entity_id = null) {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("INSERT INTO notifications (title, message, type, action_type, entity_type, entity_id) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$title, $message, $type, $action_type, $entity_type, $entity_id]);

        return true;
    } catch (PDOException $e) {
        error_log("Error adding notification: " . $e->getMessage());
        return false;
    }
}

function getNotifications($limit = 10, $unread_only = false) {
    try {
        $pdo = getConnection();

        $sql = "SELECT * FROM notifications";
        if ($unread_only) {
            $sql .= " WHERE is_read = FALSE";
        }
        $sql .= " ORDER BY created_at DESC LIMIT ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$limit]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return [];
    }
}

function getUnreadNotificationCount() {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$result['count'];
    } catch (PDOException $e) {
        error_log("Error getting unread notification count: " . $e->getMessage());
        return 0;
    }
}

function markNotificationAsRead($id) {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE id = ?");
        $stmt->execute([$id]);

        return true;
    } catch (PDOException $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

function markAllNotificationsAsRead() {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("UPDATE notifications SET is_read = TRUE WHERE is_read = FALSE");
        $stmt->execute();

        return true;
    } catch (PDOException $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

?>
