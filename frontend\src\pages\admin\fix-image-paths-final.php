<?php
/**
 * <PERSON>ript final untuk memperbaiki path gambar ke react-news/uploads
 */

require_once 'config.php';

echo "<h2>🔧 Fix Image Paths Final</h2>";

$sourceDir = __DIR__ . '/../../../uploads/';
$targetDir = __DIR__ . '/../../../react-news/uploads/';

echo "<h3>1. Directory Setup</h3>";
echo "<strong>Source Directory:</strong> " . $sourceDir . "<br>";
echo "<strong>Target Directory:</strong> " . $targetDir . "<br>";

// Create target directory if it doesn't exist
if (!is_dir($targetDir)) {
    if (mkdir($targetDir, 0755, true)) {
        echo "✅ Created target directory<br>";
    } else {
        echo "❌ Failed to create target directory<br>";
        exit(1);
    }
} else {
    echo "✅ Target directory exists<br>";
}

echo "<h3>2. Moving News Images</h3>";

$movedCount = 0;
$errorCount = 0;

if (is_dir($sourceDir)) {
    $files = glob($sourceDir . 'news_*');
    echo "<strong>News images found:</strong> " . count($files) . "<br><br>";
    
    foreach ($files as $sourceFile) {
        $filename = basename($sourceFile);
        $targetFile = $targetDir . $filename;
        
        echo "Processing: " . $filename . " ... ";
        
        if (file_exists($targetFile)) {
            echo "⚠️ Already exists in target<br>";
            continue;
        }
        
        if (copy($sourceFile, $targetFile)) {
            echo "✅ Copied successfully<br>";
            $movedCount++;
            
            // Remove original file
            if (unlink($sourceFile)) {
                echo "   Original file deleted<br>";
            }
        } else {
            echo "❌ Failed to copy<br>";
            $errorCount++;
        }
    }
    
    echo "<br><strong>Move Summary:</strong><br>";
    echo "   Moved: " . $movedCount . " files<br>";
    echo "   Errors: " . $errorCount . " files<br>";
}

echo "<h3>3. Update Database Paths</h3>";

try {
    $pdo = getConnection();
    
    // Update all image paths to use /react-news/uploads/
    $updates = [
        "UPDATE posts SET image = REPLACE(image, '/uploads/', '/react-news/uploads/') WHERE image LIKE '/uploads/%'",
        "UPDATE posts SET image = CONCAT('/react-news/uploads/', image) WHERE image NOT LIKE '/%' AND image NOT LIKE 'http%' AND image != '' AND image IS NOT NULL"
    ];
    
    $totalUpdated = 0;
    
    foreach ($updates as $sql) {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute();
        $updatedRows = $stmt->rowCount();
        $totalUpdated += $updatedRows;
        echo "✅ Updated " . $updatedRows . " records<br>";
    }
    
    echo "<strong>Total database updates:</strong> " . $totalUpdated . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>4. Verify Current State</h3>";

try {
    // Check database paths
    $stmt = $pdo->prepare("SELECT id, title, image FROM posts WHERE image IS NOT NULL AND image != '' ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $newsWithImages = $stmt->fetchAll();
    
    echo "<strong>Recent news with images:</strong><br>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Image Path</th><th>File Exists</th><th>URL Test</th></tr>";
    
    foreach ($newsWithImages as $news) {
        $imagePath = $news['image'];
        
        // Extract filename
        $filename = '';
        if (strpos($imagePath, '/react-news/uploads/') === 0) {
            $filename = str_replace('/react-news/uploads/', '', $imagePath);
        } else if (strpos($imagePath, '/uploads/') === 0) {
            $filename = str_replace('/uploads/', '', $imagePath);
        } else if (!strpos($imagePath, '/')) {
            $filename = $imagePath;
        } else {
            $filename = basename($imagePath);
        }
        
        // Check if file exists
        $filePath = $targetDir . $filename;
        $fileExists = file_exists($filePath) ? '✅ Yes' : '❌ No';
        $testUrl = "http://localhost/react-news/uploads/" . $filename;
        
        echo "<tr>";
        echo "<td>" . $news['id'] . "</td>";
        echo "<td>" . htmlspecialchars(substr($news['title'], 0, 30)) . "...</td>";
        echo "<td>" . htmlspecialchars($imagePath) . "</td>";
        echo "<td>" . $fileExists . "</td>";
        echo "<td><a href='" . $testUrl . "' target='_blank'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Verification error: " . $e->getMessage() . "<br>";
}

echo "<h3>5. Files in Target Directory</h3>";

if (is_dir($targetDir)) {
    $targetFiles = glob($targetDir . 'news_*');
    echo "<strong>News images in react-news/uploads:</strong> " . count($targetFiles) . "<br>";
    
    if (count($targetFiles) > 0) {
        echo "<strong>Recent files:</strong><br>";
        $recentFiles = array_slice($targetFiles, -5);
        foreach ($recentFiles as $file) {
            $filename = basename($file);
            $webUrl = "http://localhost/react-news/uploads/" . $filename;
            echo "   • " . $filename . " - <a href='" . $webUrl . "' target='_blank'>View</a><br>";
        }
    }
}

echo "<h3>6. Test URLs</h3>";

try {
    $stmt = $pdo->prepare("SELECT image FROM posts WHERE image LIKE '/react-news/uploads/%' LIMIT 5");
    $stmt->execute();
    $testImages = $stmt->fetchAll();
    
    echo "<strong>Test these URLs:</strong><br>";
    foreach ($testImages as $img) {
        $testUrl = "http://localhost" . $img['image'];
        echo "<a href='" . $testUrl . "' target='_blank'>" . $testUrl . "</a><br>";
    }
} catch (Exception $e) {
    echo "Could not fetch test URLs<br>";
}

echo "<br>🎉 Image path fix completed!<br>";
echo "<br><strong>Next steps:</strong><br>";
echo "1. Refresh admin dashboard news table<br>";
echo "2. Test add new news with image<br>";
echo "3. Test edit existing news<br>";
echo "4. Test delete news with image<br>";
echo "5. Check frontend news display<br>";
?>
