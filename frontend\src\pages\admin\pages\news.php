<!-- News Management -->
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Kelola Be<PERSON></h1>
                <p class="text-gray-600 mt-1">Kelola semua berita dan artikel Anda</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-3">
                <button onclick="deleteSelected()" id="delete-selected-btn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-trash mr-2"></i>
                    <PERSON><PERSON> Terpilih (<span id="selected-count">0</span>)
                </button>
                <a href="?page=add-news" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center">
                    <i class="fas fa-plus mr-2"></i>
                    Tambah Berita
                </a>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="filter-status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="filter-status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="">Semua Status</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                </select>
            </div>
            <div>
                <label for="filter-category" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                <select id="filter-category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="">Semua Kategori</option>
                </select>
            </div>
            <div>
                <label for="search-news" class="block text-sm font-medium text-gray-700 mb-2">Cari Berita</label>
                <input type="text" id="search-news" placeholder="Cari judul berita..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
            <div class="flex items-end">
                <button onclick="applyFilters()" class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
            </div>
        </div>
    </div>
    
    <!-- News Table -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <!-- Mobile scroll hint -->
        <div class="md:hidden bg-gray-50 px-4 py-2 text-sm text-gray-600 border-b">
            <i class="fas fa-arrows-alt-h mr-2"></i>
            Geser tabel ke kiri/kanan untuk melihat semua kolom
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            #
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Gambar
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Judul
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Kategori
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Views
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tgl & Waktu
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Aksi
                        </th>
                    </tr>
                </thead>
                <tbody id="news-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- Loading state -->
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mr-3"></i>
                                <span class="text-gray-500">Memuat data berita...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prev-mobile" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </button>
                    <button id="next-mobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Menampilkan <span id="showing-from" class="font-medium">1</span> sampai <span id="showing-to" class="font-medium">10</span> dari <span id="total-items" class="font-medium">0</span> hasil
                        </p>
                    </div>
                    <div>
                        <nav id="pagination" class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Pagination buttons will be generated here -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let totalPages = 1;
let selectedItems = new Set();
let allNews = [];
let filteredNews = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadNews();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="news-checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
            if (this.checked) {
                selectedItems.add(checkbox.value);
            } else {
                selectedItems.delete(checkbox.value);
            }
        });
        updateSelectedCount();
    });
    
    // Search input
    document.getElementById('search-news').addEventListener('input', debounce(applyFilters, 300));
    
    // Filter selects
    document.getElementById('filter-status').addEventListener('change', applyFilters);
    document.getElementById('filter-category').addEventListener('change', applyFilters);
}

// Load categories for filter
async function loadCategories() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_categories`);
        const select = document.getElementById('filter-category');
        
        if (data.success && data.data) {
            data.data.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

// Load news data
async function loadNews() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_news`);
        
        if (data.success && data.data) {
            allNews = data.data;
            filteredNews = [...allNews];

            // Simple log for debugging
            console.log('News data loaded:', allNews.length, 'items');

            renderNewsTable();
        } else {
            console.error('Failed to load news data:', data);
            document.getElementById('news-table-body').innerHTML =
                '<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Tidak ada data berita</td></tr>';
        }
    } catch (error) {
        console.error('Error loading news:', error);
        document.getElementById('news-table-body').innerHTML = 
            '<tr><td colspan="9" class="px-6 py-4 text-center text-red-500">Error memuat data berita</td></tr>';
    }
}

// Apply filters
function applyFilters() {
    const statusFilter = document.getElementById('filter-status').value;
    const categoryFilter = document.getElementById('filter-category').value;
    const searchFilter = document.getElementById('search-news').value.toLowerCase();
    
    filteredNews = allNews.filter(news => {
        const matchesStatus = !statusFilter || news.status === statusFilter;
        const matchesCategory = !categoryFilter || news.category_id == categoryFilter;
        const matchesSearch = !searchFilter || news.title.toLowerCase().includes(searchFilter);
        
        return matchesStatus && matchesCategory && matchesSearch;
    });
    
    currentPage = 1;
    renderNewsTable();
}

// Render news table
function renderNewsTable() {
    const itemsPerPage = 10;
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = filteredNews.slice(startIndex, endIndex);
    
    const tbody = document.getElementById('news-table-body');
    
    if (pageItems.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">Tidak ada data yang sesuai filter</td></tr>';
        return;
    }
    
    tbody.innerHTML = pageItems.map((news, index) => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" name="news-checkbox" value="${news.id}" class="rounded border-gray-300 text-primary focus:ring-primary" onchange="updateSelection(this)">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${startIndex + index + 1}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                    ${news.image || news.image_base64 ?
                        `<img src="${getImageUrl(news.image, news.image_base64)}" alt="${news.title}" class="w-full h-full object-cover"
                         onerror="this.parentElement.innerHTML='<div class=\\'w-full h-full flex items-center justify-center text-gray-400\\'><i class=\\'fas fa-image\\'></i><br><small>Error</small></div>'">` :
                        `<div class="w-full h-full flex items-center justify-center text-gray-400">
                            <i class="fas fa-image"></i><br><small>No image</small>
                        </div>`
                    }
                </div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900 max-w-xs truncate" title="${news.title}">
                    ${news.title}
                </div>
                <div class="text-sm text-gray-500 max-w-xs truncate" title="${news.description || ''}">
                    ${news.description || ''}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ${news.category_name || 'Umum'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    news.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                }">
                    ${news.status === 'published' ? 'Published' : 'Draft'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                ${news.views || 0}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                ${formatDate(news.created_at)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                <div class="flex items-center justify-center space-x-2">
                    <button onclick="viewNews(${news.id})" class="text-blue-600 hover:text-blue-900" title="Lihat">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="editNews(${news.id})" class="text-indigo-600 hover:text-indigo-900" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteNews(${news.id})" class="text-red-600 hover:text-red-900" title="Hapus">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    updatePagination();
}

// Update selection
function updateSelection(checkbox) {
    if (checkbox.checked) {
        selectedItems.add(checkbox.value);
    } else {
        selectedItems.delete(checkbox.value);
    }
    updateSelectedCount();
}

// Update selected count
function updateSelectedCount() {
    const count = selectedItems.size;
    document.getElementById('selected-count').textContent = count;
    document.getElementById('delete-selected-btn').disabled = count === 0;
    
    // Update select all checkbox
    const allCheckboxes = document.querySelectorAll('input[name="news-checkbox"]');
    const selectAllCheckbox = document.getElementById('select-all');
    
    if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Update pagination
function updatePagination() {
    const itemsPerPage = 10;
    totalPages = Math.ceil(filteredNews.length / itemsPerPage);
    
    // Update showing info
    const startIndex = (currentPage - 1) * itemsPerPage + 1;
    const endIndex = Math.min(currentPage * itemsPerPage, filteredNews.length);
    
    document.getElementById('showing-from').textContent = filteredNews.length > 0 ? startIndex : 0;
    document.getElementById('showing-to').textContent = endIndex;
    document.getElementById('total-items').textContent = filteredNews.length;
    
    // Generate pagination buttons
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.className = `relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''}`;
    prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => changePage(currentPage - 1);
    pagination.appendChild(prevBtn);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                i === currentPage 
                    ? 'z-10 bg-primary border-primary text-white' 
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
            }`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => changePage(i);
            pagination.appendChild(pageBtn);
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            const dots = document.createElement('span');
            dots.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
            dots.textContent = '...';
            pagination.appendChild(dots);
        }
    }
    
    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.className = `relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''}`;
    nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => changePage(currentPage + 1);
    pagination.appendChild(nextBtn);
}

// Change page
function changePage(page) {
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderNewsTable();
    }
}

// Action functions
function viewNews(id) {
    // Open news detail in new tab
    window.open(`../../../user/data-news.js?id=${id}`, '_blank');
}

function editNews(id) {
    window.location.href = `?page=edit-news&id=${id}`;
}

async function deleteNews(id) {
    // Get news title for confirmation
    const news = allNews.find(n => n.id == id);
    const newsTitle = news ? news.title : 'berita ini';

    const result = await Swal.fire({
        title: 'Hapus Berita?',
        html: `Apakah Anda yakin ingin menghapus berita:<br><strong>"${newsTitle}"</strong><br><br>Data akan dihapus secara permanen dan tidak dapat dikembalikan.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal',
        reverseButtons: true
    });

    if (!result.isConfirmed) return;

    try {
        // Show loading
        Swal.fire({
            title: 'Menghapus...',
            text: 'Sedang menghapus berita',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        const formData = new FormData();
        formData.append('action', 'delete_news');
        formData.append('id', id);

        console.log('Deleting news ID:', id);

        const apiResult = await safeFetch(API_BASE, {
            method: 'POST',
            body: formData
        });

        console.log('Delete result:', apiResult);

        if (apiResult.success) {
            await Swal.fire({
                title: 'Berhasil!',
                text: 'Berita berhasil dihapus secara permanen',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
            loadNews(); // Reload the news list
        } else {
            await Swal.fire({
                title: 'Gagal!',
                text: apiResult.message || 'Gagal menghapus berita',
                icon: 'error'
            });
        }
    } catch (error) {
        console.error('Error deleting news:', error);
        await Swal.fire({
            title: 'Error!',
            text: 'Terjadi kesalahan saat menghapus berita',
            icon: 'error'
        });
    }
}

async function deleteSelected() {
    if (selectedItems.size === 0) {
        await Swal.fire({
            title: 'Tidak ada berita dipilih',
            text: 'Pilih berita yang ingin dihapus terlebih dahulu',
            icon: 'info'
        });
        return;
    }

    // Get selected news titles for confirmation
    const selectedNews = Array.from(selectedItems).map(id => {
        const news = allNews.find(n => n.id == id);
        return news ? news.title : `ID: ${id}`;
    });

    const result = await Swal.fire({
        title: `Hapus ${selectedItems.size} Berita?`,
        html: `Apakah Anda yakin ingin menghapus berita berikut:<br><br>` +
              selectedNews.map(title => `• ${title}`).join('<br>') +
              `<br><br><strong>Data akan dihapus secara permanen dan tidak dapat dikembalikan.</strong>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: `Ya, Hapus ${selectedItems.size} Berita!`,
        cancelButtonText: 'Batal',
        reverseButtons: true
    });

    if (!result.isConfirmed) return;

    try {
        // Show loading
        Swal.fire({
            title: 'Menghapus...',
            text: `Sedang menghapus ${selectedItems.size} berita`,
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        console.log('Deleting selected news IDs:', Array.from(selectedItems));

        const promises = Array.from(selectedItems).map(id => {
            const formData = new FormData();
            formData.append('action', 'delete_news');
            formData.append('id', id);
            return safeFetch(API_BASE, { method: 'POST', body: formData });
        });

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;

        console.log('Bulk delete results:', { successCount, failCount, total: results.length });

        if (successCount > 0) {
            let message = `${successCount} berita berhasil dihapus`;
            if (failCount > 0) {
                message += `, ${failCount} gagal dihapus`;
            }

            await Swal.fire({
                title: successCount === results.length ? 'Berhasil!' : 'Sebagian Berhasil',
                text: message,
                icon: successCount === results.length ? 'success' : 'warning',
                timer: 3000,
                showConfirmButton: false
            });

            selectedItems.clear();
            updateSelectedCount();
            loadNews();
        } else {
            await Swal.fire({
                title: 'Gagal!',
                text: 'Semua berita gagal dihapus',
                icon: 'error'
            });
        }
    } catch (error) {
        console.error('Error deleting selected news:', error);
        await Swal.fire({
            title: 'Error!',
            text: 'Terjadi kesalahan saat menghapus berita',
            icon: 'error'
        });
    }
}

// Helper functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Debug function to test image URL processing
function debugImageUrl(imagePath) {
    console.log('=== DEBUG IMAGE URL ===');
    console.log('Input path:', imagePath);

    if (!imagePath) {
        console.log('Result: null (no path)');
        return null;
    }

    let result;

    if (imagePath.startsWith('http')) {
        result = imagePath;
        console.log('Type: Full URL');
    } else if (imagePath.startsWith('/uploads/')) {
        result = `http://localhost/react-news${imagePath}`;
        console.log('Type: New uploads path');
    } else if (imagePath.startsWith('/react-news/uploads/')) {
        result = `http://localhost${imagePath}`;
        console.log('Type: React-news uploads path');
    } else if (imagePath.startsWith('assets/news/')) {
        const filename = imagePath.replace('assets/news/', '');
        result = `http://localhost/react-news/uploads/${filename}`;
        console.log('Type: Assets path converted');
    } else if (!imagePath.includes('/')) {
        result = `http://localhost/react-news/uploads/${imagePath}`;
        console.log('Type: Filename only');
    } else {
        result = `http://localhost/react-news/uploads/${imagePath}`;
        console.log('Type: Fallback');
    }

    console.log('Result URL:', result);
    console.log('======================');
    return result;
}

// Enhanced getImageUrl function - Handle both file path and base64
function getImageUrl(imagePath, imageBase64 = '') {
    console.log('Processing image path:', imagePath);
    console.log('Has base64 data:', !!imageBase64);

    // If no image data at all
    if (!imagePath && !imageBase64) return null;

    // If it's already a data URL (base64), return as is
    if (imagePath && imagePath.startsWith('data:')) {
        console.log('Using data URL:', imagePath.substring(0, 50) + '...');
        return imagePath;
    }

    // If it's already a full HTTP URL, return as is
    if (imagePath && imagePath.startsWith('http')) {
        console.log('Using full URL:', imagePath);
        return imagePath;
    }

    // If we have a file path, try to use it
    if (imagePath) {
        let filename = '';

        if (imagePath.startsWith('/react-news/frontend/uploads/')) {
            filename = imagePath.replace('/react-news/frontend/uploads/', '');
        } else if (imagePath.startsWith('/react-news/uploads/')) {
            filename = imagePath.replace('/react-news/uploads/', '');
        } else if (imagePath.startsWith('/uploads/')) {
            filename = imagePath.replace('/uploads/', '');
        } else if (imagePath.startsWith('assets/news/')) {
            filename = imagePath.replace('assets/news/', '');
        } else if (!imagePath.includes('/')) {
            // Just filename
            filename = imagePath;
        } else {
            // Extract filename from any other path
            filename = imagePath.split('/').pop();
        }

        const url = `http://localhost/react-news/uploads/${filename}`;
        console.log('Using file URL:', url);
        return url;
    }

    // If no file path but we have base64, create data URL
    if (imageBase64) {
        const dataUrl = `data:image/jpeg;base64,${imageBase64}`;
        console.log('Using base64 data URL');
        return dataUrl;
    }

    return null;
}



function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
