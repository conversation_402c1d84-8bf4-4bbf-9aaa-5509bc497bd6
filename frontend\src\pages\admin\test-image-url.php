<?php
/**
 * Script untuk test URL gambar secara langsung
 */

require_once 'config.php';

echo "<h2>🧪 Test Image URL</h2>";

// Test specific file
$testFile = 'news_1752651695_2152.webp';
$uploadDir = __DIR__ . '/../../../uploads/';
$filePath = $uploadDir . $testFile;

echo "<h3>1. File System Check</h3>";
echo "<strong>Upload Directory:</strong> " . $uploadDir . "<br>";
echo "<strong>Directory Exists:</strong> " . (is_dir($uploadDir) ? 'Yes' : 'No') . "<br>";
echo "<strong>Test File:</strong> " . $testFile . "<br>";
echo "<strong>Full File Path:</strong> " . $filePath . "<br>";
echo "<strong>File Exists:</strong> " . (file_exists($filePath) ? 'Yes' : 'No') . "<br>";

if (file_exists($filePath)) {
    echo "<strong>File Size:</strong> " . filesize($filePath) . " bytes<br>";
    echo "<strong>File Permissions:</strong> " . substr(sprintf('%o', fileperms($filePath)), -4) . "<br>";
}

echo "<h3>2. URL Tests</h3>";

// Test different URL formats
$testUrls = [
    "http://localhost/react-news/uploads/" . $testFile,
    "http://localhost/uploads/" . $testFile,
    "http://127.0.0.1/react-news/uploads/" . $testFile,
    "http://127.0.0.1/uploads/" . $testFile
];

foreach ($testUrls as $url) {
    echo "<strong>Testing URL:</strong> <a href='$url' target='_blank'>$url</a><br>";
    
    // Test with cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "   HTTP Status: " . $httpCode . "<br>";
    echo "   Result: " . ($httpCode == 200 ? '✅ Success' : '❌ Failed') . "<br><br>";
}

echo "<h3>3. Server Configuration Check</h3>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "<strong>Script Path:</strong> " . __FILE__ . "<br>";
echo "<strong>Current Directory:</strong> " . __DIR__ . "<br>";

// Check if .htaccess exists
$htaccessPath = $_SERVER['DOCUMENT_ROOT'] . '/react-news/.htaccess';
echo "<strong>.htaccess exists:</strong> " . (file_exists($htaccessPath) ? 'Yes' : 'No') . "<br>";

if (file_exists($htaccessPath)) {
    echo "<strong>.htaccess content:</strong><br>";
    echo "<pre>" . htmlspecialchars(file_get_contents($htaccessPath)) . "</pre>";
}

echo "<h3>4. Directory Listing</h3>";
if (is_dir($uploadDir)) {
    $files = scandir($uploadDir);
    echo "<strong>Files in uploads directory:</strong><br>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $fullPath = $uploadDir . $file;
            $size = is_file($fullPath) ? filesize($fullPath) : 0;
            echo "   • " . $file . " (" . $size . " bytes)<br>";
        }
    }
}

echo "<h3>5. Direct Image Test</h3>";
if (file_exists($filePath)) {
    echo "<strong>Direct image display test:</strong><br>";
    $imageData = file_get_contents($filePath);
    $base64 = base64_encode($imageData);
    $mimeType = mime_content_type($filePath);
    
    echo "<img src='data:$mimeType;base64,$base64' style='max-width: 200px; max-height: 200px;' alt='Test Image'><br>";
    echo "<strong>MIME Type:</strong> " . $mimeType . "<br>";
    echo "<strong>Base64 length:</strong> " . strlen($base64) . " characters<br>";
}
?>
