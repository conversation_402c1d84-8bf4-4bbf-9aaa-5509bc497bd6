<?php
/**
 * <PERSON>ript untuk memindahkan gambar dari root/uploads ke react-news/uploads
 */

require_once 'config.php';

echo "<h2>📁 Move Images to react-news/uploads</h2>";

$sourceDir = __DIR__ . '/../../../uploads/';
$targetDir = __DIR__ . '/../../../react-news/uploads/';

echo "<h3>1. Directory Check</h3>";
echo "<strong>Source Directory:</strong> " . $sourceDir . "<br>";
echo "<strong>Target Directory:</strong> " . $targetDir . "<br>";

// Create target directory if it doesn't exist
if (!is_dir($targetDir)) {
    if (mkdir($targetDir, 0755, true)) {
        echo "✅ Created target directory: " . $targetDir . "<br>";
    } else {
        echo "❌ Failed to create target directory<br>";
        exit(1);
    }
} else {
    echo "✅ Target directory exists<br>";
}

echo "<h3>2. Moving News Images</h3>";

if (is_dir($sourceDir)) {
    $files = glob($sourceDir . 'news_*');
    echo "<strong>News images found in source:</strong> " . count($files) . "<br><br>";
    
    $movedCount = 0;
    $errorCount = 0;
    
    foreach ($files as $sourceFile) {
        $filename = basename($sourceFile);
        $targetFile = $targetDir . $filename;
        
        echo "Moving: " . $filename . " ... ";
        
        if (file_exists($targetFile)) {
            echo "⚠️ Already exists in target<br>";
            continue;
        }
        
        if (copy($sourceFile, $targetFile)) {
            echo "✅ Success<br>";
            $movedCount++;
            
            // Remove original file after successful copy
            if (unlink($sourceFile)) {
                echo "   Original file deleted<br>";
            } else {
                echo "   ⚠️ Could not delete original file<br>";
            }
        } else {
            echo "❌ Failed<br>";
            $errorCount++;
        }
    }
    
    echo "<br><strong>Summary:</strong><br>";
    echo "   Moved: " . $movedCount . " files<br>";
    echo "   Errors: " . $errorCount . " files<br>";
} else {
    echo "❌ Source directory not found<br>";
}

echo "<h3>3. Update Database Paths</h3>";

try {
    $pdo = getConnection();
    
    // Update posts table - change /uploads/ paths to /react-news/uploads/
    $stmt = $pdo->prepare("UPDATE posts SET image = REPLACE(image, '/uploads/', '/react-news/uploads/') WHERE image LIKE '/uploads/%'");
    $result = $stmt->execute();
    $updatedRows = $stmt->rowCount();
    
    if ($result) {
        echo "✅ Updated " . $updatedRows . " database paths<br>";
    } else {
        echo "❌ Failed to update database paths<br>";
    }
    
    // Show sample of updated paths
    echo "<br><strong>Sample updated paths:</strong><br>";
    $stmt = $pdo->prepare("SELECT id, title, image FROM posts WHERE image LIKE '/react-news/uploads/%' LIMIT 5");
    $stmt->execute();
    $samples = $stmt->fetchAll();
    
    foreach ($samples as $sample) {
        echo "   ID " . $sample['id'] . ": " . htmlspecialchars(substr($sample['title'], 0, 30)) . "...<br>";
        echo "   Image: " . htmlspecialchars($sample['image']) . "<br>";
        echo "   URL: http://localhost" . $sample['image'] . "<br><br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>4. Verify Files in Target Directory</h3>";

if (is_dir($targetDir)) {
    $targetFiles = glob($targetDir . 'news_*');
    echo "<strong>News images in target directory:</strong> " . count($targetFiles) . "<br>";
    
    if (count($targetFiles) > 0) {
        echo "<strong>Recent files:</strong><br>";
        $recentFiles = array_slice($targetFiles, -5);
        foreach ($recentFiles as $file) {
            $filename = basename($file);
            $webUrl = "http://localhost/react-news/uploads/" . $filename;
            echo "   • " . $filename . " - <a href='" . $webUrl . "' target='_blank'>Test</a><br>";
        }
    }
}

echo "<h3>5. Test URLs</h3>";

try {
    $stmt = $pdo->prepare("SELECT image FROM posts WHERE image LIKE '/react-news/uploads/%' LIMIT 3");
    $stmt->execute();
    $testImages = $stmt->fetchAll();
    
    foreach ($testImages as $img) {
        $testUrl = "http://localhost" . $img['image'];
        echo "<a href='" . $testUrl . "' target='_blank'>" . $testUrl . "</a><br>";
    }
} catch (Exception $e) {
    echo "Could not fetch test URLs<br>";
}

echo "<br>🎉 Migration completed!<br>";
echo "<br><strong>Next steps:</strong><br>";
echo "1. Test images in admin dashboard<br>";
echo "2. Test add new news with image<br>";
echo "3. Test edit news with image<br>";
echo "4. Test delete news with image<br>";
?>
